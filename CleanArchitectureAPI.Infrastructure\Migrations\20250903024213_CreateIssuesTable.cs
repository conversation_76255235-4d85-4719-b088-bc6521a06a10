﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CleanArchitectureAPI.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class CreateIssuesTable : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Comment_Issue_IssueId",
                table: "Comment");

            migrationBuilder.DropForeignKey(
                name: "FK_Comment_Issue_IssueId1",
                table: "Comment");

            migrationBuilder.DropForeignKey(
                name: "FK_Issue_IssueTypes_IssueTypeId",
                table: "Issue");

            migrationBuilder.DropForeignKey(
                name: "FK_Issue_Resolutions_ResolutionId",
                table: "Issue");

            migrationBuilder.DropForeignKey(
                name: "FK_Issue_Statuses_StatusId",
                table: "Issue");

            migrationBuilder.DropForeignKey(
                name: "FK_Issue_Users_AssigneeId",
                table: "Issue");

            migrationBuilder.DropForeignKey(
                name: "FK_Issue_Users_ReporterId",
                table: "Issue");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueCategory_Issue_IssueId",
                table: "IssueCategory");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueMilestone_Issue_IssueId",
                table: "IssueMilestone");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueVersion_Issue_IssueId",
                table: "IssueVersion");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Issue",
                table: "Issue");

            migrationBuilder.RenameTable(
                name: "Issue",
                newName: "Issues");

            migrationBuilder.RenameIndex(
                name: "IX_Issue_StatusId",
                table: "Issues",
                newName: "IX_Issues_StatusId");

            migrationBuilder.RenameIndex(
                name: "IX_Issue_ResolutionId",
                table: "Issues",
                newName: "IX_Issues_ResolutionId");

            migrationBuilder.RenameIndex(
                name: "IX_Issue_ReporterId",
                table: "Issues",
                newName: "IX_Issues_ReporterId");

            migrationBuilder.RenameIndex(
                name: "IX_Issue_IssueTypeId",
                table: "Issues",
                newName: "IX_Issues_IssueTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_Issue_AssigneeId",
                table: "Issues",
                newName: "IX_Issues_AssigneeId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Issues",
                table: "Issues",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Comment_Issues_IssueId",
                table: "Comment",
                column: "IssueId",
                principalTable: "Issues",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Comment_Issues_IssueId1",
                table: "Comment",
                column: "IssueId1",
                principalTable: "Issues",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_IssueCategory_Issues_IssueId",
                table: "IssueCategory",
                column: "IssueId",
                principalTable: "Issues",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueMilestone_Issues_IssueId",
                table: "IssueMilestone",
                column: "IssueId",
                principalTable: "Issues",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Issues_IssueTypes_IssueTypeId",
                table: "Issues",
                column: "IssueTypeId",
                principalTable: "IssueTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Issues_Resolutions_ResolutionId",
                table: "Issues",
                column: "ResolutionId",
                principalTable: "Resolutions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Issues_Statuses_StatusId",
                table: "Issues",
                column: "StatusId",
                principalTable: "Statuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Issues_Users_AssigneeId",
                table: "Issues",
                column: "AssigneeId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Issues_Users_ReporterId",
                table: "Issues",
                column: "ReporterId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueVersion_Issues_IssueId",
                table: "IssueVersion",
                column: "IssueId",
                principalTable: "Issues",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Comment_Issues_IssueId",
                table: "Comment");

            migrationBuilder.DropForeignKey(
                name: "FK_Comment_Issues_IssueId1",
                table: "Comment");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueCategory_Issues_IssueId",
                table: "IssueCategory");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueMilestone_Issues_IssueId",
                table: "IssueMilestone");

            migrationBuilder.DropForeignKey(
                name: "FK_Issues_IssueTypes_IssueTypeId",
                table: "Issues");

            migrationBuilder.DropForeignKey(
                name: "FK_Issues_Resolutions_ResolutionId",
                table: "Issues");

            migrationBuilder.DropForeignKey(
                name: "FK_Issues_Statuses_StatusId",
                table: "Issues");

            migrationBuilder.DropForeignKey(
                name: "FK_Issues_Users_AssigneeId",
                table: "Issues");

            migrationBuilder.DropForeignKey(
                name: "FK_Issues_Users_ReporterId",
                table: "Issues");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueVersion_Issues_IssueId",
                table: "IssueVersion");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Issues",
                table: "Issues");

            migrationBuilder.RenameTable(
                name: "Issues",
                newName: "Issue");

            migrationBuilder.RenameIndex(
                name: "IX_Issues_StatusId",
                table: "Issue",
                newName: "IX_Issue_StatusId");

            migrationBuilder.RenameIndex(
                name: "IX_Issues_ResolutionId",
                table: "Issue",
                newName: "IX_Issue_ResolutionId");

            migrationBuilder.RenameIndex(
                name: "IX_Issues_ReporterId",
                table: "Issue",
                newName: "IX_Issue_ReporterId");

            migrationBuilder.RenameIndex(
                name: "IX_Issues_IssueTypeId",
                table: "Issue",
                newName: "IX_Issue_IssueTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_Issues_AssigneeId",
                table: "Issue",
                newName: "IX_Issue_AssigneeId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Issue",
                table: "Issue",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Comment_Issue_IssueId",
                table: "Comment",
                column: "IssueId",
                principalTable: "Issue",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Comment_Issue_IssueId1",
                table: "Comment",
                column: "IssueId1",
                principalTable: "Issue",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Issue_IssueTypes_IssueTypeId",
                table: "Issue",
                column: "IssueTypeId",
                principalTable: "IssueTypes",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Issue_Resolutions_ResolutionId",
                table: "Issue",
                column: "ResolutionId",
                principalTable: "Resolutions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Issue_Statuses_StatusId",
                table: "Issue",
                column: "StatusId",
                principalTable: "Statuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Issue_Users_AssigneeId",
                table: "Issue",
                column: "AssigneeId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Issue_Users_ReporterId",
                table: "Issue",
                column: "ReporterId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueCategory_Issue_IssueId",
                table: "IssueCategory",
                column: "IssueId",
                principalTable: "Issue",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueMilestone_Issue_IssueId",
                table: "IssueMilestone",
                column: "IssueId",
                principalTable: "Issue",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueVersion_Issue_IssueId",
                table: "IssueVersion",
                column: "IssueId",
                principalTable: "Issue",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
