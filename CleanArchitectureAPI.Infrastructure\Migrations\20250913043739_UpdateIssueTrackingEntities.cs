﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CleanArchitectureAPI.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class UpdateIssueTrackingEntities : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Comment_Issue_IssueId",
                table: "Comment");

            migrationBuilder.DropForeignKey(
                name: "FK_Comment_Issue_IssueId1",
                table: "Comment");

            migrationBuilder.DropForeignKey(
                name: "FK_Comment_Users_AuthorId",
                table: "Comment");

            migrationBuilder.DropForeignKey(
                name: "FK_Issue_IssueType_IssueTypeId",
                table: "Issue");

            migrationBuilder.DropForeignKey(
                name: "FK_Issue_Resolution_ResolutionId",
                table: "Issue");

            migrationBuilder.DropForeignKey(
                name: "FK_Issue_Statuses_StatusId",
                table: "Issue");

            migrationBuilder.DropForeignKey(
                name: "FK_Issue_Users_AssigneeId",
                table: "Issue");

            migrationBuilder.DropForeignKey(
                name: "FK_Issue_Users_ReporterId",
                table: "Issue");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueCategory_Issue_IssueId",
                table: "IssueCategory");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueMilestone_Issue_IssueId",
                table: "IssueMilestone");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueVersion_Issue_IssueId",
                table: "IssueVersion");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Issue",
                table: "Issue");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Comment",
                table: "Comment");

            migrationBuilder.DropIndex(
                name: "IX_Comment_IssueId",
                table: "Comment");

            migrationBuilder.RenameTable(
                name: "Issue",
                newName: "Issues");

            migrationBuilder.RenameTable(
                name: "Comment",
                newName: "Comments");

            migrationBuilder.RenameIndex(
                name: "IX_Issue_StatusId",
                table: "Issues",
                newName: "IX_Issues_StatusId");

            migrationBuilder.RenameIndex(
                name: "IX_Issue_ResolutionId",
                table: "Issues",
                newName: "IX_Issues_ResolutionId");

            migrationBuilder.RenameIndex(
                name: "IX_Issue_ReporterId",
                table: "Issues",
                newName: "IX_Issues_ReporterId");

            migrationBuilder.RenameIndex(
                name: "IX_Issue_IssueTypeId",
                table: "Issues",
                newName: "IX_Issues_IssueTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_Issue_AssigneeId",
                table: "Issues",
                newName: "IX_Issues_AssigneeId");

            migrationBuilder.RenameColumn(
                name: "IssueId1",
                table: "Comments",
                newName: "ChangeGroupId");

            migrationBuilder.RenameIndex(
                name: "IX_Comment_IssueId1",
                table: "Comments",
                newName: "IX_Comments_ChangeGroupId");

            migrationBuilder.RenameIndex(
                name: "IX_Comment_AuthorId",
                table: "Comments",
                newName: "IX_Comments_AuthorId");

            migrationBuilder.AddColumn<string>(
                name: "CommentType",
                table: "Comments",
                type: "varchar(50)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsEdited",
                table: "Comments",
                type: "bit",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastEditedAt",
                table: "Comments",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_Issues",
                table: "Issues",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Comments",
                table: "Comments",
                column: "Id");

            migrationBuilder.CreateTable(
                name: "ChangeGroups",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    IssueId = table.Column<int>(type: "int", nullable: false),
                    AuthorId = table.Column<int>(type: "int", nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", nullable: true),
                    ChangeType = table.Column<string>(type: "varchar(50)", nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedById = table.Column<int>(type: "int", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifiedById = table.Column<int>(type: "int", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedById = table.Column<int>(type: "int", nullable: true),
                    ProjectId = table.Column<int>(type: "int", nullable: false),
                    ProjectKey = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ChangeGroups", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ChangeGroups_Issues_IssueId",
                        column: x => x.IssueId,
                        principalTable: "Issues",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ChangeGroups_Users_AuthorId",
                        column: x => x.AuthorId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "Worklogs",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    IssueId = table.Column<int>(type: "int", nullable: false),
                    AuthorId = table.Column<int>(type: "int", nullable: false),
                    WorklogBody = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    StartDate = table.Column<DateTime>(type: "datetime2", nullable: false),
                    EndDate = table.Column<DateTime>(type: "datetime2", nullable: true),
                    TimeWorkedSeconds = table.Column<int>(type: "int", nullable: false),
                    RemainingEstimateSeconds = table.Column<int>(type: "int", nullable: true),
                    IsPublic = table.Column<bool>(type: "bit", nullable: false),
                    WorklogType = table.Column<string>(type: "varchar(50)", nullable: false),
                    IsEdited = table.Column<bool>(type: "bit", nullable: false),
                    LastEditedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedById = table.Column<int>(type: "int", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifiedById = table.Column<int>(type: "int", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedById = table.Column<int>(type: "int", nullable: true),
                    ProjectId = table.Column<int>(type: "int", nullable: false),
                    ProjectKey = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Worklogs", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Worklogs_Issues_IssueId",
                        column: x => x.IssueId,
                        principalTable: "Issues",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Worklogs_Users_AuthorId",
                        column: x => x.AuthorId,
                        principalTable: "Users",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "ChangeItems",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    GroupId = table.Column<int>(type: "int", nullable: false),
                    FieldType = table.Column<string>(type: "varchar(50)", nullable: false),
                    Field = table.Column<string>(type: "varchar(255)", nullable: false),
                    FieldDisplayName = table.Column<string>(type: "nvarchar(255)", nullable: true),
                    OldValue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    OldString = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NewValue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NewString = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DisplayOrder = table.Column<int>(type: "int", nullable: false),
                    IsDeleted = table.Column<bool>(type: "bit", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreatedById = table.Column<int>(type: "int", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    LastModifiedById = table.Column<int>(type: "int", nullable: true),
                    IsActive = table.Column<bool>(type: "bit", nullable: false),
                    DeletedAt = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DeletedById = table.Column<int>(type: "int", nullable: true),
                    ProjectId = table.Column<int>(type: "int", nullable: false),
                    ProjectKey = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ChangeItems", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ChangeItems_ChangeGroups_GroupId",
                        column: x => x.GroupId,
                        principalTable: "ChangeGroups",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Comments_IssueId_CreatedAt",
                table: "Comments",
                columns: new[] { "IssueId", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_ChangeGroups_AuthorId",
                table: "ChangeGroups",
                column: "AuthorId");

            migrationBuilder.CreateIndex(
                name: "IX_ChangeGroups_IssueId_CreatedAt",
                table: "ChangeGroups",
                columns: new[] { "IssueId", "CreatedAt" });

            migrationBuilder.CreateIndex(
                name: "IX_ChangeItems_GroupId_DisplayOrder",
                table: "ChangeItems",
                columns: new[] { "GroupId", "DisplayOrder" });

            migrationBuilder.CreateIndex(
                name: "IX_Worklogs_AuthorId",
                table: "Worklogs",
                column: "AuthorId");

            migrationBuilder.CreateIndex(
                name: "IX_Worklogs_IssueId_StartDate",
                table: "Worklogs",
                columns: new[] { "IssueId", "StartDate" });

            migrationBuilder.AddForeignKey(
                name: "FK_Comments_ChangeGroups_ChangeGroupId",
                table: "Comments",
                column: "ChangeGroupId",
                principalTable: "ChangeGroups",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_Comments_Issues_IssueId",
                table: "Comments",
                column: "IssueId",
                principalTable: "Issues",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Comments_Users_AuthorId",
                table: "Comments",
                column: "AuthorId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueCategory_Issues_IssueId",
                table: "IssueCategory",
                column: "IssueId",
                principalTable: "Issues",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueMilestone_Issues_IssueId",
                table: "IssueMilestone",
                column: "IssueId",
                principalTable: "Issues",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Issues_IssueType_IssueTypeId",
                table: "Issues",
                column: "IssueTypeId",
                principalTable: "IssueType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Issues_Resolution_ResolutionId",
                table: "Issues",
                column: "ResolutionId",
                principalTable: "Resolution",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Issues_Statuses_StatusId",
                table: "Issues",
                column: "StatusId",
                principalTable: "Statuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Issues_Users_AssigneeId",
                table: "Issues",
                column: "AssigneeId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Issues_Users_ReporterId",
                table: "Issues",
                column: "ReporterId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueVersion_Issues_IssueId",
                table: "IssueVersion",
                column: "IssueId",
                principalTable: "Issues",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Comments_ChangeGroups_ChangeGroupId",
                table: "Comments");

            migrationBuilder.DropForeignKey(
                name: "FK_Comments_Issues_IssueId",
                table: "Comments");

            migrationBuilder.DropForeignKey(
                name: "FK_Comments_Users_AuthorId",
                table: "Comments");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueCategory_Issues_IssueId",
                table: "IssueCategory");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueMilestone_Issues_IssueId",
                table: "IssueMilestone");

            migrationBuilder.DropForeignKey(
                name: "FK_Issues_IssueType_IssueTypeId",
                table: "Issues");

            migrationBuilder.DropForeignKey(
                name: "FK_Issues_Resolution_ResolutionId",
                table: "Issues");

            migrationBuilder.DropForeignKey(
                name: "FK_Issues_Statuses_StatusId",
                table: "Issues");

            migrationBuilder.DropForeignKey(
                name: "FK_Issues_Users_AssigneeId",
                table: "Issues");

            migrationBuilder.DropForeignKey(
                name: "FK_Issues_Users_ReporterId",
                table: "Issues");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueVersion_Issues_IssueId",
                table: "IssueVersion");

            migrationBuilder.DropTable(
                name: "ChangeItems");

            migrationBuilder.DropTable(
                name: "Worklogs");

            migrationBuilder.DropTable(
                name: "ChangeGroups");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Issues",
                table: "Issues");

            migrationBuilder.DropPrimaryKey(
                name: "PK_Comments",
                table: "Comments");

            migrationBuilder.DropIndex(
                name: "IX_Comments_IssueId_CreatedAt",
                table: "Comments");

            migrationBuilder.DropColumn(
                name: "CommentType",
                table: "Comments");

            migrationBuilder.DropColumn(
                name: "IsEdited",
                table: "Comments");

            migrationBuilder.DropColumn(
                name: "LastEditedAt",
                table: "Comments");

            migrationBuilder.RenameTable(
                name: "Issues",
                newName: "Issue");

            migrationBuilder.RenameTable(
                name: "Comments",
                newName: "Comment");

            migrationBuilder.RenameIndex(
                name: "IX_Issues_StatusId",
                table: "Issue",
                newName: "IX_Issue_StatusId");

            migrationBuilder.RenameIndex(
                name: "IX_Issues_ResolutionId",
                table: "Issue",
                newName: "IX_Issue_ResolutionId");

            migrationBuilder.RenameIndex(
                name: "IX_Issues_ReporterId",
                table: "Issue",
                newName: "IX_Issue_ReporterId");

            migrationBuilder.RenameIndex(
                name: "IX_Issues_IssueTypeId",
                table: "Issue",
                newName: "IX_Issue_IssueTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_Issues_AssigneeId",
                table: "Issue",
                newName: "IX_Issue_AssigneeId");

            migrationBuilder.RenameColumn(
                name: "ChangeGroupId",
                table: "Comment",
                newName: "IssueId1");

            migrationBuilder.RenameIndex(
                name: "IX_Comments_ChangeGroupId",
                table: "Comment",
                newName: "IX_Comment_IssueId1");

            migrationBuilder.RenameIndex(
                name: "IX_Comments_AuthorId",
                table: "Comment",
                newName: "IX_Comment_AuthorId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Issue",
                table: "Issue",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Comment",
                table: "Comment",
                column: "Id");

            migrationBuilder.CreateIndex(
                name: "IX_Comment_IssueId",
                table: "Comment",
                column: "IssueId");

            migrationBuilder.AddForeignKey(
                name: "FK_Comment_Issue_IssueId",
                table: "Comment",
                column: "IssueId",
                principalTable: "Issue",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Comment_Issue_IssueId1",
                table: "Comment",
                column: "IssueId1",
                principalTable: "Issue",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Comment_Users_AuthorId",
                table: "Comment",
                column: "AuthorId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Restrict);

            migrationBuilder.AddForeignKey(
                name: "FK_Issue_IssueType_IssueTypeId",
                table: "Issue",
                column: "IssueTypeId",
                principalTable: "IssueType",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Issue_Resolution_ResolutionId",
                table: "Issue",
                column: "ResolutionId",
                principalTable: "Resolution",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Issue_Statuses_StatusId",
                table: "Issue",
                column: "StatusId",
                principalTable: "Statuses",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_Issue_Users_AssigneeId",
                table: "Issue",
                column: "AssigneeId",
                principalTable: "Users",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Issue_Users_ReporterId",
                table: "Issue",
                column: "ReporterId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueCategory_Issue_IssueId",
                table: "IssueCategory",
                column: "IssueId",
                principalTable: "Issue",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueMilestone_Issue_IssueId",
                table: "IssueMilestone",
                column: "IssueId",
                principalTable: "Issue",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueVersion_Issue_IssueId",
                table: "IssueVersion",
                column: "IssueId",
                principalTable: "Issue",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
