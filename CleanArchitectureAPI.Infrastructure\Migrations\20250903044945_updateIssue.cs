﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CleanArchitectureAPI.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class updateIssue : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Issues_Resolutions_ResolutionId",
                table: "Issues");

            migrationBuilder.RenameColumn(
                name: "ResolutionId",
                table: "Issues",
                newName: "PriorityId");

            migrationBuilder.RenameIndex(
                name: "IX_Issues_ResolutionId",
                table: "Issues",
                newName: "IX_Issues_PriorityId");

            migrationBuilder.AddForeignKey(
                name: "FK_Issues_Priorities_PriorityId",
                table: "Issues",
                column: "PriorityId",
                principalTable: "Priorities",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeign<PERSON>ey(
                name: "FK_Issues_Priorities_PriorityId",
                table: "Issues");

            migrationBuilder.RenameColumn(
                name: "PriorityId",
                table: "Issues",
                newName: "ResolutionId");

            migrationBuilder.RenameIndex(
                name: "IX_Issues_PriorityId",
                table: "Issues",
                newName: "IX_Issues_ResolutionId");

            migrationBuilder.AddForeignKey(
                name: "FK_Issues_Resolutions_ResolutionId",
                table: "Issues",
                column: "ResolutionId",
                principalTable: "Resolutions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
