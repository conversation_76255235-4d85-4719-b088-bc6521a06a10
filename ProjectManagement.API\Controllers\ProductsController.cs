using CleanArchitectureAPI.Application.DTOs;
using CleanArchitectureAPI.Application.Interfaces;
using CleanArchitectureAPI.Domain.Exceptions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace CleanArchitecture.API.Controllers
{
	[Route("api/[controller]")]
	[ApiController]
	public class ProductsController : ControllerBase
	{
		private readonly IProductService _productService;

		public ProductsController(IProductService productService)
		{
			_productService = productService;
		}

		[HttpGet]
		public async Task<ActionResult<IEnumerable<ProductDto>>> GetProducts()
		{
			try
			{
				var products = await _productService.GetAllProductsAsync();
				return Ok(products);
			}
			catch (Exception ex)
			{
				return StatusCode(500, $"Internal server error: {ex.Message}");
			}
		}

		[HttpGet("{id}")]
		public async Task<ActionResult<ProductDto>> GetProduct(int id)
		{
			try
			{
				var product = await _productService.GetProductByIdAsync(id);
				return Ok(product);
			}
			catch (ProductNotFoundException)
			{
				return NotFound($"Product with ID {id} not found.");
			}
			catch (Exception ex)
			{
				return StatusCode(500, $"Internal server error: {ex.Message}");
			}
		}

		[HttpPost]
		public async Task<ActionResult<ProductDto>> CreateProduct(CreateProductDto createProductDto)
		{
			try
			{
				if (!ModelState.IsValid)
					return BadRequest(ModelState);

				var product = await _productService.CreateProductAsync(createProductDto);
				return CreatedAtAction(nameof(GetProduct), new { id = product.Id }, product);
			}
			catch (Exception ex)
			{
				return StatusCode(500, $"Internal server error: {ex.Message}");
			}
		}

		[HttpPut("{id}")]
		public async Task<ActionResult<ProductDto>> UpdateProduct(int id, UpdateProductDto updateProductDto)
		{
			try
			{
				if (!ModelState.IsValid)
					return BadRequest(ModelState);

				var product = await _productService.UpdateProductAsync(id, updateProductDto);
				return Ok(product);
			}
			catch (ProductNotFoundException)
			{
				return NotFound($"Product with ID {id} not found.");
			}
			catch (Exception ex)
			{
				return StatusCode(500, $"Internal server error: {ex.Message}");
			}
		}

		[HttpDelete("{id}")]
		public async Task<ActionResult> DeleteProduct(int id)
		{
			try
			{
				await _productService.DeleteProductAsync(id);
				return NoContent();
			}
			catch (ProductNotFoundException)
			{
				return NotFound($"Product with ID {id} not found.");
			}
			catch (Exception ex)
			{
				return StatusCode(500, $"Internal server error: {ex.Message}");
			}
		}
	}
}
