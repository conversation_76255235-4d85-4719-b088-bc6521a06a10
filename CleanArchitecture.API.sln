﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.12.35707.178
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CleanArchitecture.API", "CleanArchitecture.API\CleanArchitecture.API.csproj", "{5F239F6F-73EC-4C83-AFA3-4DC0ED73C962}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CleanArchitectureAPI.Application", "CleanArchitectureAPI.Application\CleanArchitectureAPI.Application.csproj", "{482AFFD8-79DE-4620-9855-AEB3377DA624}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CleanArchitectureAPI.Domain", "CleanArchitectureAPI.Domain\CleanArchitectureAPI.Domain.csproj", "{BDAF0EAB-9080-4ADB-BA66-5C0C8CC044AB}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CleanArchitectureAPI.Infrastructure", "CleanArchitectureAPI.Infrastructure\CleanArchitectureAPI.Infrastructure.csproj", "{874D1179-0A54-4BE5-851A-F7D6E966D26B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CleanArchitecture.Shared", "CleanArchitecture.Shared\CleanArchitecture.Shared.csproj", "{7D791C1E-D989-4CA9-BE73-E2A74A3137A1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CleanArchitectureAPI.Tests", "CleanArchitectureAPI.Tests\CleanArchitectureAPI.Tests.csproj", "{C322BDD8-A746-4B77-B402-4679EC40FD61}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{5F239F6F-73EC-4C83-AFA3-4DC0ED73C962}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5F239F6F-73EC-4C83-AFA3-4DC0ED73C962}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5F239F6F-73EC-4C83-AFA3-4DC0ED73C962}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5F239F6F-73EC-4C83-AFA3-4DC0ED73C962}.Release|Any CPU.Build.0 = Release|Any CPU
		{482AFFD8-79DE-4620-9855-AEB3377DA624}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{482AFFD8-79DE-4620-9855-AEB3377DA624}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{482AFFD8-79DE-4620-9855-AEB3377DA624}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{482AFFD8-79DE-4620-9855-AEB3377DA624}.Release|Any CPU.Build.0 = Release|Any CPU
		{BDAF0EAB-9080-4ADB-BA66-5C0C8CC044AB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BDAF0EAB-9080-4ADB-BA66-5C0C8CC044AB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BDAF0EAB-9080-4ADB-BA66-5C0C8CC044AB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BDAF0EAB-9080-4ADB-BA66-5C0C8CC044AB}.Release|Any CPU.Build.0 = Release|Any CPU
		{874D1179-0A54-4BE5-851A-F7D6E966D26B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{874D1179-0A54-4BE5-851A-F7D6E966D26B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{874D1179-0A54-4BE5-851A-F7D6E966D26B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{874D1179-0A54-4BE5-851A-F7D6E966D26B}.Release|Any CPU.Build.0 = Release|Any CPU
		{7D791C1E-D989-4CA9-BE73-E2A74A3137A1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{7D791C1E-D989-4CA9-BE73-E2A74A3137A1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{7D791C1E-D989-4CA9-BE73-E2A74A3137A1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{7D791C1E-D989-4CA9-BE73-E2A74A3137A1}.Release|Any CPU.Build.0 = Release|Any CPU
		{C322BDD8-A746-4B77-B402-4679EC40FD61}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C322BDD8-A746-4B77-B402-4679EC40FD61}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C322BDD8-A746-4B77-B402-4679EC40FD61}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C322BDD8-A746-4B77-B402-4679EC40FD61}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {18259D89-5001-4F1E-A74F-7EDB7AD2AE23}
	EndGlobalSection
EndGlobal
