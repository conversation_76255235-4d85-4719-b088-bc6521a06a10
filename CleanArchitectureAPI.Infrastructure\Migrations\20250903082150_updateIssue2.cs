﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace CleanArchitectureAPI.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class updateIssue2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_IssueCategory_Categories_CategoryId",
                table: "IssueCategory");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueCategory_Issues_IssueId",
                table: "IssueCategory");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueMilestone_Issues_IssueId",
                table: "IssueMilestone");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueMilestone_Milestones_MilestoneId",
                table: "IssueMilestone");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueVersion_Issues_IssueId",
                table: "IssueVersion");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueVersion_Versions_VersionId",
                table: "IssueVersion");

            migrationBuilder.DropPrimaryKey(
                name: "PK_IssueVersion",
                table: "IssueVersion");

            migrationBuilder.DropPrimaryKey(
                name: "PK_IssueMilestone",
                table: "IssueMilestone");

            migrationBuilder.DropPrimaryKey(
                name: "PK_IssueCategory",
                table: "IssueCategory");

            migrationBuilder.DropColumn(
                name: "StartDate",
                table: "Issues");

            migrationBuilder.RenameTable(
                name: "IssueVersion",
                newName: "IssueVersions");

            migrationBuilder.RenameTable(
                name: "IssueMilestone",
                newName: "IssueMilestones");

            migrationBuilder.RenameTable(
                name: "IssueCategory",
                newName: "IssueCategories");

            migrationBuilder.RenameIndex(
                name: "IX_IssueVersion_VersionId",
                table: "IssueVersions",
                newName: "IX_IssueVersions_VersionId");

            migrationBuilder.RenameIndex(
                name: "IX_IssueVersion_IssueId",
                table: "IssueVersions",
                newName: "IX_IssueVersions_IssueId");

            migrationBuilder.RenameIndex(
                name: "IX_IssueMilestone_MilestoneId",
                table: "IssueMilestones",
                newName: "IX_IssueMilestones_MilestoneId");

            migrationBuilder.RenameIndex(
                name: "IX_IssueMilestone_IssueId",
                table: "IssueMilestones",
                newName: "IX_IssueMilestones_IssueId");

            migrationBuilder.RenameIndex(
                name: "IX_IssueCategory_IssueId",
                table: "IssueCategories",
                newName: "IX_IssueCategories_IssueId");

            migrationBuilder.RenameIndex(
                name: "IX_IssueCategory_CategoryId",
                table: "IssueCategories",
                newName: "IX_IssueCategories_CategoryId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_IssueVersions",
                table: "IssueVersions",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_IssueMilestones",
                table: "IssueMilestones",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_IssueCategories",
                table: "IssueCategories",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_IssueCategories_Categories_CategoryId",
                table: "IssueCategories",
                column: "CategoryId",
                principalTable: "Categories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueCategories_Issues_IssueId",
                table: "IssueCategories",
                column: "IssueId",
                principalTable: "Issues",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueMilestones_Issues_IssueId",
                table: "IssueMilestones",
                column: "IssueId",
                principalTable: "Issues",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueMilestones_Milestones_MilestoneId",
                table: "IssueMilestones",
                column: "MilestoneId",
                principalTable: "Milestones",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueVersions_Issues_IssueId",
                table: "IssueVersions",
                column: "IssueId",
                principalTable: "Issues",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueVersions_Versions_VersionId",
                table: "IssueVersions",
                column: "VersionId",
                principalTable: "Versions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_IssueCategories_Categories_CategoryId",
                table: "IssueCategories");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueCategories_Issues_IssueId",
                table: "IssueCategories");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueMilestones_Issues_IssueId",
                table: "IssueMilestones");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueMilestones_Milestones_MilestoneId",
                table: "IssueMilestones");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueVersions_Issues_IssueId",
                table: "IssueVersions");

            migrationBuilder.DropForeignKey(
                name: "FK_IssueVersions_Versions_VersionId",
                table: "IssueVersions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_IssueVersions",
                table: "IssueVersions");

            migrationBuilder.DropPrimaryKey(
                name: "PK_IssueMilestones",
                table: "IssueMilestones");

            migrationBuilder.DropPrimaryKey(
                name: "PK_IssueCategories",
                table: "IssueCategories");

            migrationBuilder.RenameTable(
                name: "IssueVersions",
                newName: "IssueVersion");

            migrationBuilder.RenameTable(
                name: "IssueMilestones",
                newName: "IssueMilestone");

            migrationBuilder.RenameTable(
                name: "IssueCategories",
                newName: "IssueCategory");

            migrationBuilder.RenameIndex(
                name: "IX_IssueVersions_VersionId",
                table: "IssueVersion",
                newName: "IX_IssueVersion_VersionId");

            migrationBuilder.RenameIndex(
                name: "IX_IssueVersions_IssueId",
                table: "IssueVersion",
                newName: "IX_IssueVersion_IssueId");

            migrationBuilder.RenameIndex(
                name: "IX_IssueMilestones_MilestoneId",
                table: "IssueMilestone",
                newName: "IX_IssueMilestone_MilestoneId");

            migrationBuilder.RenameIndex(
                name: "IX_IssueMilestones_IssueId",
                table: "IssueMilestone",
                newName: "IX_IssueMilestone_IssueId");

            migrationBuilder.RenameIndex(
                name: "IX_IssueCategories_IssueId",
                table: "IssueCategory",
                newName: "IX_IssueCategory_IssueId");

            migrationBuilder.RenameIndex(
                name: "IX_IssueCategories_CategoryId",
                table: "IssueCategory",
                newName: "IX_IssueCategory_CategoryId");

            migrationBuilder.AddColumn<DateTime>(
                name: "StartDate",
                table: "Issues",
                type: "datetime2",
                nullable: true);

            migrationBuilder.AddPrimaryKey(
                name: "PK_IssueVersion",
                table: "IssueVersion",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_IssueMilestone",
                table: "IssueMilestone",
                column: "Id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_IssueCategory",
                table: "IssueCategory",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_IssueCategory_Categories_CategoryId",
                table: "IssueCategory",
                column: "CategoryId",
                principalTable: "Categories",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueCategory_Issues_IssueId",
                table: "IssueCategory",
                column: "IssueId",
                principalTable: "Issues",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueMilestone_Issues_IssueId",
                table: "IssueMilestone",
                column: "IssueId",
                principalTable: "Issues",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueMilestone_Milestones_MilestoneId",
                table: "IssueMilestone",
                column: "MilestoneId",
                principalTable: "Milestones",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueVersion_Issues_IssueId",
                table: "IssueVersion",
                column: "IssueId",
                principalTable: "Issues",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_IssueVersion_Versions_VersionId",
                table: "IssueVersion",
                column: "VersionId",
                principalTable: "Versions",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
